package s3manager

import (
	"encoding/json"
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.mtk.zone/mt-public/s3manager/internal/providers/ovh"
)

// ovhProjectsCmd represents the ovh-projects command
var ovhProjectsCmd = &cobra.Command{
	Use:   "ovh-projects",
	Short: "List OVH projects",
	Long:  "List OVH projects using the configured OVH API credentials.",
	RunE:  runOvhProjects,
}

var fakeTestData string

func init() {
	rootCmd.AddCommand(ovhProjectsCmd)
	ovhProjectsCmd.Flags().StringVar(&fakeTestData, "fake-test", "", "Inject fake test data (empty|invalid|mixed|null|large|error)")
}

func runOvhProjects(cmd *cobra.Command, args []string) error {
	// Access the global configuration
	config := GetGlobalConfig()

	provider, err := ovh.NewProvider(config.OvhEndpoint, config.OvhApplicationKey, config.OvhApplicationSecret, config.OvhConsumerKey, config.OvhProjectId)
	if err != nil {
		return fmt.Errorf("failed to create OVH provider: %w", err)
	}

	var result interface{}

	// Check if we should inject fake test data
	if fakeTestData != "" {
		result, err = getFakeTestData(fakeTestData)
		if err != nil {
			fmt.Printf("Fake test failed: %v", err)
			return err
		}
	} else {
		result, err = provider.ExecApi("get", "/cloud/project", "")
		if err != nil {
			fmt.Printf("API call failed: %v", err)
			return err
		}
	}

	// Pretty print the result
	resultJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		fmt.Printf("%+v\n", result)
	} else {
		fmt.Printf("%s\n", string(resultJSON))
	}

	// Type assert to []interface{} and then convert each element to string
	if projects, ok := result.([]interface{}); ok {
		for i, project := range projects {
			if projectID, ok := project.(string); ok {
				fmt.Printf("%d: %s\n", i, projectID)
			} else {
				return fmt.Errorf("%d: %v (unexpected type: %T)", i, project, project)
			}
		}
	} else {
		fmt.Printf("Unexpected result format, got type: %T\n", result)
	}

	return nil
}

// getFakeTestData returns fake data for testing edge cases
func getFakeTestData(testType string) (interface{}, error) {
	switch testType {
	case "empty":
		return []interface{}{}, nil
	case "invalid":
		return map[string]interface{}{"error": "invalid format", "code": 500}, nil
	case "mixed":
		return []interface{}{
			"valid-project-1",
			123456, // This will cause type assertion error
			"valid-project-2",
			map[string]string{"invalid": "object"},
		}, nil
	case "null":
		return nil, nil
	case "large":
		projects := make([]interface{}, 100)
		for i := 0; i < 100; i++ {
			projects[i] = fmt.Sprintf("project-%d", i)
		}
		return projects, nil
	case "error":
		return nil, fmt.Errorf("simulated API error: authentication failed")
	default:
		return nil, fmt.Errorf("unknown test type: %s", testType)
	}
}
