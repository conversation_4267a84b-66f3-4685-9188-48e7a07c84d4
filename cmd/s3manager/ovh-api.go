package s3manager

import (
	"encoding/json"
	"fmt"

	"github.com/spf13/cobra"
	"gitlab.mtk.zone/mt-public/s3manager/internal/providers/ovh"
)

// ovhApiCmd represents the ovh-api command
var ovhApiCmd = &cobra.Command{
	Use:   "ovh-api [method] [path]",
	Short: "Make direct OVH API calls for testing",
	Long: `Make direct OVH API calls using configured credentials.

Examples:
  s3manager ovh-api get /cloud/project
  s3manager ovh-api get /me
  s3manager ovh-api post /cloud/project/PROJECT_ID/user '{"username":"test-user"}'`,
	Args: cobra.RangeArgs(2, 3),
	RunE: runOvhApi,
}

// ovhAPIProjectCmd represents the ovh-api-project command
var ovhApiProjectCmd = &cobra.Command{
	Use:   "ovh-api-project [method] [path]",
	Short: "Make OVH API calls with project prefix for testing",
	Long: `Make OVH API calls with /cloud/project/{projectId} prefix using configured credentials.

Examples:
  s3manager ovh-api-project get /user
  s3manager ovh-api-project get /user/532182
  s3manager ovh-api-project post /user '{"username":"test-user"}'`,
	Args: cobra.RangeArgs(2, 3),
	RunE: runOvhApiProject,
}

func init() {
	rootCmd.AddCommand(ovhApiCmd)
	rootCmd.AddCommand(ovhApiProjectCmd)
}

func runOvhApi(cmd *cobra.Command, args []string) error {
	config := GetGlobalConfig()

	provider, err := ovh.NewProvider(config.OvhEndpoint, config.OvhApplicationKey, config.OvhApplicationSecret, config.OvhConsumerKey, config.OvhProjectId)
	if err != nil {
		return fmt.Errorf("failed to create OVH provider: %w", err)
	}

	method := args[0]
	path := args[1]
	body := ""
	if len(args) > 2 {
		body = args[2]
	}

	// Print the request details
	fmt.Printf("%s %s", method, path)
	if body != "" {
		fmt.Println(body)
	} else {
		fmt.Println("")
	}

	// Execute the API call and get raw data
	result, err := provider.ExecApi(method, path, body)
	if err != nil {
		fmt.Printf("API call failed: %v", err)
		return fmt.Errorf("OVH API call failed: %w", err)
	}

	// Pretty print the result
	resultJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		fmt.Printf("%+v\n", result)
	} else {
		fmt.Printf("%s\n", string(resultJSON))
	}

	return nil
}

func runOvhApiProject(cmd *cobra.Command, args []string) error {
	config := GetGlobalConfig()

	provider, err := ovh.NewProvider(config.OvhEndpoint, config.OvhApplicationKey, config.OvhApplicationSecret, config.OvhConsumerKey, config.OvhProjectId)
	if err != nil {
		return fmt.Errorf("failed to create OVH provider: %w", err)
	}

	method := args[0]
	path := args[1]
	body := ""
	if len(args) > 2 {
		body = args[2]
	}

	// Print the request details (will show the final path with project prefix)
	fmt.Printf("%s /cloud/project/%s%s", method, config.OvhProjectId, path)
	if body != "" {
		fmt.Println(body)
	} else {
		fmt.Println("")
	}

	// Execute the API call and get raw data
	result, err := provider.ExecApiProject(method, path, body)
	if err != nil {
		fmt.Printf("API call failed: %v", err)
		return fmt.Errorf("OVH API call failed: %w", err)
	}

	// Pretty print the result
	resultJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		fmt.Printf("%+v\n", result)
	} else {
		fmt.Printf("%s\n", string(resultJSON))
	}

	return nil
}
