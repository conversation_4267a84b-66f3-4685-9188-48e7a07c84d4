package s3manager

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"strings"
	"testing"

	ovhProvider "gitlab.mtk.zone/mt-public/s3manager/internal/providers/ovh"
)

// MockOVHProvider is a simple mock that implements the ExecApi method
type MockOVHProvider struct {
	execApiFunc func(method, path, body string) (any, error)
}

func (m *MockOVHProvider) ExecApi(method, path, body string) (any, error) {
	if m.execApiFunc != nil {
		return m.execApiFunc(method, path, body)
	}
	return nil, fmt.Errorf("mock not configured")
}

// mockRunOvhProjects is a testable version of runOvhProjects that accepts a provider
func mockRunOvhProjects(provider *MockOVHProvider) error {
	result, err := provider.ExecApi("get", "/cloud/project", "")
	if err != nil {
		fmt.Printf("API call failed: %v", err)
		return err
	}

	// Pretty print the result
	resultJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		fmt.Printf("%+v\n", result)
	} else {
		fmt.Printf("%s\n", string(resultJSON))
	}

	return nil
}

// Helper function to capture stdout for testing
func captureStdout(fn func()) string {
	originalStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	output := make(chan string)
	go func() {
		var buf bytes.Buffer
		io.Copy(&buf, r)
		output <- buf.String()
	}()

	fn()

	w.Close()
	os.Stdout = originalStdout
	return <-output
}

func TestOVHProviderCreation(t *testing.T) {
	t.Run("should return an error for missing application key", func(t *testing.T) {
		// Test with empty application key
		_, err := ovhProvider.NewProvider("ovh-eu", "", "secret", "consumer", "project")
		if err == nil {
			t.Fatal("expected error but got nil")
		}
		if !strings.Contains(err.Error(), "OVH application key not configured") {
			t.Errorf("expected error to contain 'OVH application key not configured', got: %v", err)
		}
	})

	t.Run("should return an error for missing application secret", func(t *testing.T) {
		// Test with empty application secret
		_, err := ovhProvider.NewProvider("ovh-eu", "key", "", "consumer", "project")
		if err == nil {
			t.Fatal("expected error but got nil")
		}
		if !strings.Contains(err.Error(), "OVH application secret not configured") {
			t.Errorf("expected error to contain 'OVH application secret not configured', got: %v", err)
		}
	})

	t.Run("should return an error for missing consumer key", func(t *testing.T) {
		// Test with empty consumer key
		_, err := ovhProvider.NewProvider("ovh-eu", "key", "secret", "", "project")
		if err == nil {
			t.Fatal("expected error but got nil")
		}
		if !strings.Contains(err.Error(), "OVH consumer key not configured") {
			t.Errorf("expected error to contain 'OVH consumer key not configured', got: %v", err)
		}
	})
}

func TestExecApiWithMock(t *testing.T) {
	t.Run("should successfully call the API and return project list", func(t *testing.T) {
		// Create a mock provider with expected response
		expectedProjects := []string{"project-1", "project-2", "project-3"}
		mockProvider := &MockOVHProvider{
			execApiFunc: func(method, path, body string) (any, error) {
				if method != "get" {
					t.Errorf("expected method 'get', got: %s", method)
				}
				if path != "/cloud/project" {
					t.Errorf("expected path '/cloud/project', got: %s", path)
				}
				if body != "" {
					t.Errorf("expected empty body, got: %s", body)
				}
				return expectedProjects, nil
			},
		}

		// Test the mock function directly
		result, err := mockProvider.ExecApi("get", "/cloud/project", "")
		if err != nil {
			t.Fatalf("expected no error, got: %v", err)
		}

		resultSlice, ok := result.([]string)
		if !ok {
			t.Fatalf("expected result to be []string, got: %T", result)
		}

		if len(resultSlice) != len(expectedProjects) {
			t.Fatalf("expected %d projects, got %d", len(expectedProjects), len(resultSlice))
		}

		for i, project := range expectedProjects {
			if resultSlice[i] != project {
				t.Errorf("expected project[%d] to be %s, got %s", i, project, resultSlice[i])
			}
		}
	})

	t.Run("should handle API errors correctly", func(t *testing.T) {
		// Create a mock provider that returns an error
		mockProvider := &MockOVHProvider{
			execApiFunc: func(method, path, body string) (any, error) {
				return nil, fmt.Errorf("API connection failed")
			},
		}

		// Test the error handling
		result, err := mockProvider.ExecApi("get", "/cloud/project", "")
		if err == nil {
			t.Fatal("expected error but got nil")
		}
		if !strings.Contains(err.Error(), "API connection failed") {
			t.Errorf("expected error to contain 'API connection failed', got: %v", err)
		}
		if result != nil {
			t.Errorf("expected result to be nil, got: %v", result)
		}
	})

	t.Run("should test the complete runOvhProjects logic with mock", func(t *testing.T) {
		// Create a mock provider with expected response
		expectedProjects := []string{"project-1", "project-2", "project-3"}
		mockProvider := &MockOVHProvider{
			execApiFunc: func(method, path, body string) (any, error) {
				return expectedProjects, nil
			},
		}

		// Capture stdout and test the mockRunOvhProjects function
		outputStr := captureStdout(func() {
			err := mockRunOvhProjects(mockProvider)
			if err != nil {
				t.Errorf("expected no error, got: %v", err)
			}
		})

		// Verify the JSON output
		expectedJSON := `[
  "project-1",
  "project-2",
  "project-3"
]`
		if !strings.Contains(outputStr, expectedJSON) {
			t.Errorf("expected output to contain JSON:\n%s\nGot:\n%s", expectedJSON, outputStr)
		}
	})
}

func TestRunOvhProjectsBehavior(t *testing.T) {
	t.Run("should handle JSON marshaling correctly", func(t *testing.T) {
		// Test the JSON marshaling logic that's used in runOvhProjects
		testData := []string{"project-1", "project-2", "project-3"}

		resultJSON, err := json.MarshalIndent(testData, "", "  ")
		if err != nil {
			t.Fatalf("expected no error, got: %v", err)
		}

		expectedJSON := `[
  "project-1",
  "project-2",
  "project-3"
]`
		if string(resultJSON) != expectedJSON {
			t.Errorf("expected JSON:\n%s\nGot:\n%s", expectedJSON, string(resultJSON))
		}
	})

	t.Run("should handle empty project list", func(t *testing.T) {
		// Test with empty project list
		testData := []string{}

		resultJSON, err := json.MarshalIndent(testData, "", "  ")
		if err != nil {
			t.Fatalf("expected no error, got: %v", err)
		}
		if string(resultJSON) != "[]" {
			t.Errorf("expected '[]', got: %s", string(resultJSON))
		}
	})
}
