package s3manager

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"os"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	ovhProvider "gitlab.mtk.zone/mt-public/s3manager/internal/providers/ovh"
)

// MockOVHProvider is a simple mock that implements the ExecApi method
type MockOVHProvider struct {
	execApiFunc func(method, path, body string) (any, error)
}

func (m *MockOVHProvider) ExecApi(method, path, body string) (any, error) {
	if m.execApiFunc != nil {
		return m.execApiFunc(method, path, body)
	}
	return nil, fmt.Errorf("mock not configured")
}

// mockRunOvhProjects is a testable version of runOvhProjects that accepts a provider
func mockRunOvhProjects(provider *MockOVHProvider) error {
	result, err := provider.ExecApi("get", "/cloud/project", "")
	if err != nil {
		fmt.Printf("API call failed: %v", err)
		return err
	}

	// Pretty print the result
	resultJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		fmt.Printf("%+v\n", result)
	} else {
		fmt.Printf("%s\n", string(resultJSON))
	}

	return nil
}

var _ = Describe("OVH Projects Command", func() {
	var (
		originalStdout *os.File
		r              *os.File
		w              *os.File
		output         chan string
	)

	BeforeEach(func() {
		// Capture stdout for testing output
		originalStdout = os.Stdout
		r, w, _ = os.Pipe()
		os.Stdout = w

		output = make(chan string)
		go func() {
			var buf bytes.Buffer
			io.Copy(&buf, r)
			output <- buf.String()
		}()

		// Reset global config for each test
		globalConfig = GlobalConfig{
			OvhEndpoint:          "ovh-eu",
			OvhApplicationKey:    "test-app-key",
			OvhApplicationSecret: "test-app-secret",
			OvhConsumerKey:       "test-consumer-key",
			OvhProjectId:         "test-project-id",
		}
	})

	AfterEach(func() {
		// Restore stdout
		w.Close()
		os.Stdout = originalStdout
		r.Close()
	})

	Context("when testing OVH provider creation", func() {
		It("should return an error for missing application key", func() {
			// Test with empty application key
			_, err := ovhProvider.NewProvider("ovh-eu", "", "secret", "consumer", "project")
			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(ContainSubstring("OVH application key not configured"))
		})

		It("should return an error for missing application secret", func() {
			// Test with empty application secret
			_, err := ovhProvider.NewProvider("ovh-eu", "key", "", "consumer", "project")
			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(ContainSubstring("OVH application secret not configured"))
		})

		It("should return an error for missing consumer key", func() {
			// Test with empty consumer key
			_, err := ovhProvider.NewProvider("ovh-eu", "key", "secret", "", "project")
			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(ContainSubstring("OVH consumer key not configured"))
		})
	})

	Context("when testing the ExecApi function with mock", func() {
		It("should successfully call the API and return project list", func() {
			// Create a mock provider with expected response
			expectedProjects := []string{"project-1", "project-2", "project-3"}
			mockProvider := &MockOVHProvider{
				execApiFunc: func(method, path, body string) (any, error) {
					Expect(method).To(Equal("get"))
					Expect(path).To(Equal("/cloud/project"))
					Expect(body).To(Equal(""))
					return expectedProjects, nil
				},
			}

			// Test the mock function directly
			result, err := mockProvider.ExecApi("get", "/cloud/project", "")
			Expect(err).ToNot(HaveOccurred())
			Expect(result).To(Equal(expectedProjects))
		})

		It("should handle API errors correctly", func() {
			// Create a mock provider that returns an error
			mockProvider := &MockOVHProvider{
				execApiFunc: func(method, path, body string) (any, error) {
					return nil, fmt.Errorf("API connection failed")
				},
			}

			// Test the error handling
			result, err := mockProvider.ExecApi("get", "/cloud/project", "")
			Expect(err).To(HaveOccurred())
			Expect(err.Error()).To(ContainSubstring("API connection failed"))
			Expect(result).To(BeNil())
		})

		It("should test the complete runOvhProjects logic with mock", func() {
			// Capture stdout to verify output
			w.Close()
			<-output

			// Create a new pipe for this test
			r, w, _ = os.Pipe()
			os.Stdout = w

			output = make(chan string)
			go func() {
				var buf bytes.Buffer
				io.Copy(&buf, r)
				output <- buf.String()
			}()

			// Create a mock provider with expected response
			expectedProjects := []string{"project-1", "project-2", "project-3"}
			mockProvider := &MockOVHProvider{
				execApiFunc: func(method, path, body string) (any, error) {
					return expectedProjects, nil
				},
			}

			// Test the mockRunOvhProjects function
			err := mockRunOvhProjects(mockProvider)
			Expect(err).ToNot(HaveOccurred())

			// Close writer and get output
			w.Close()
			outputStr := <-output

			// Verify the JSON output
			expectedJSON := `[
  "project-1",
  "project-2",
  "project-3"
]`
			Expect(outputStr).To(ContainSubstring(expectedJSON))
		})
	})

	Context("when testing the runOvhProjects function behavior", func() {
		It("should handle JSON marshaling correctly", func() {
			// Test the JSON marshaling logic that's used in runOvhProjects
			testData := []string{"project-1", "project-2", "project-3"}

			resultJSON, err := json.MarshalIndent(testData, "", "  ")
			Expect(err).ToNot(HaveOccurred())

			expectedJSON := `[
  "project-1",
  "project-2",
  "project-3"
]`
			Expect(string(resultJSON)).To(Equal(expectedJSON))
		})

		It("should handle empty project list", func() {
			// Test with empty project list
			testData := []string{}

			resultJSON, err := json.MarshalIndent(testData, "", "  ")
			Expect(err).ToNot(HaveOccurred())
			Expect(string(resultJSON)).To(Equal("[]"))
		})
	})
})
