package s3manager

import (
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

// GlobalConfig holds configuration values that are available to all subcommands.
// Configuration can be set via:
// 1. Environment variables
// 2. Command line flags
// 3. Configuration file (YAML format)
// 4. Default values
//
// Priority order (highest to lowest):
// 1. Command line flags
// 2. Environment variables
// 3. Configuration file
// 4. Default values
//
// Environment variable examples:
//
//	OVH_APPLICATION_KEY=your-key
//	OVH_APPLICATION_SECRET=your-secret
//	OVH_CONSUMER_KEY=your-consumer-key
//	OVH_ENDPOINT=ovh-eu
//
// Configuration file example (~/.s3manager.yaml):
//
//	ovh:
//	  application_key: "your-key"
//	  application_secret: "your-secret"
//	  consumer_key: "your-consumer-key"
//	  endpoint: "ovh-eu"
type GlobalConfig struct {
	// OVH API Configuration
	OvhEndpoint          string
	OvhApplicationKey    string
	OvhApplicationSecret string
	OvhConsumerKey       string
	OvhProjectId         string

	// General Configuration
	ConfigFile string
}

// Global configuration instance
var globalConfig GlobalConfig

var rootCmd = &cobra.Command{
	Use:   "s3manager",
	Short: "S3Manager controller and CLI tool",
	Long: `S3Manager is a Kubernetes operator for managing S3 repositories across multiple providers.
It provides both a controller for managing S3Repository custom resources and CLI commands
for administrative tasks.`,
	PersistentPreRunE: initializeConfig,
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() error {
	return rootCmd.Execute()
}

// initializeConfig initializes the global configuration from environment variables and flags
func initializeConfig(cmd *cobra.Command, args []string) error {
	// Initialize viper
	viper.AutomaticEnv()

	// Set environment variable prefix
	viper.SetEnvPrefix("")

	// Bind environment variables to configuration fields
	viper.BindEnv("ovh.endpoint", "OVH_ENDPOINT")
	viper.BindEnv("ovh.application_key", "OVH_APPLICATION_KEY")
	viper.BindEnv("ovh.application_secret", "OVH_APPLICATION_SECRET")
	viper.BindEnv("ovh.consumer_key", "OVH_CONSUMER_KEY")
	viper.BindEnv("ovh.project_id", "OVH_PROJECT_ID")

	// Set defaults
	viper.SetDefault("ovh.endpoint", "ovh-eu")

	// Load configuration from config file if specified
	if configFile := viper.GetString("config"); configFile != "" {
		viper.SetConfigFile(configFile)
		if err := viper.ReadInConfig(); err != nil {
			return err
		}
	}

	// Populate global config struct
	globalConfig.OvhEndpoint = viper.GetString("ovh.endpoint")
	globalConfig.OvhApplicationKey = viper.GetString("ovh.application_key")
	globalConfig.OvhApplicationSecret = viper.GetString("ovh.application_secret")
	globalConfig.OvhConsumerKey = viper.GetString("ovh.consumer_key")
	globalConfig.OvhProjectId = viper.GetString("ovh.project_id")
	globalConfig.ConfigFile = viper.GetString("config")

	return nil
}

// GetGlobalConfig returns the global configuration instance
func GetGlobalConfig() *GlobalConfig {
	return &globalConfig
}

func init() {
	// Add persistent flags that are available to all subcommands
	rootCmd.PersistentFlags().StringVar(&globalConfig.ConfigFile, "config", "", "config file (default is $HOME/.s3manager.yaml)")

	// OVH API flags
	rootCmd.PersistentFlags().String("ovh-endpoint", "ovh-eu", "OVH API endpoint (can also be set via OVH_ENDPOINT env var)")
	rootCmd.PersistentFlags().String("ovh-application-key", "", "OVH API application key (can also be set via OVH_APPLICATION_KEY env var)")
	rootCmd.PersistentFlags().String("ovh-application-secret", "", "OVH API application secret (can also be set via OVH_APPLICATION_SECRET env var)")
	rootCmd.PersistentFlags().String("ovh-consumer-key", "", "OVH API consumer key (can also be set via OVH_CONSUMER_KEY env var)")
	rootCmd.PersistentFlags().String("ovh-project-id", "", "OVH project ID (can also be set via OVH_PROJECT_ID env var)")

	// Bind flags to viper
	viper.BindPFlag("ovh.endpoint", rootCmd.PersistentFlags().Lookup("ovh-endpoint"))
	viper.BindPFlag("ovh.application_key", rootCmd.PersistentFlags().Lookup("ovh-application-key"))
	viper.BindPFlag("ovh.application_secret", rootCmd.PersistentFlags().Lookup("ovh-application-secret"))
	viper.BindPFlag("ovh.consumer_key", rootCmd.PersistentFlags().Lookup("ovh-consumer-key"))
	viper.BindPFlag("ovh.project_id", rootCmd.PersistentFlags().Lookup("ovh-project-id"))
}
