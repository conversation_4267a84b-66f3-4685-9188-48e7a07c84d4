package ovh

import (
	"encoding/json"
	"fmt"
)

// MockProvider implements the same interface as Provider but returns fake data
type Mo<PERSON><PERSON>rov<PERSON> struct {
	endpoint          string
	applicationKey    string
	applicationSecret string
	consumerKey       string
	projectId         string
	
	// Fields to control what fake data to return
	FakeApiResponses map[string]interface{}
	FakeApiErrors    map[string]error
	ShouldFail       bool
	FailureMessage   string
}

func NewMockProvider(endpoint, applicationKey, applicationSecret, consumerKey, projectId string) MockProvider {
	return MockProvider{
		endpoint:          endpoint,
		applicationKey:    applicationKey,
		applicationSecret: applicationSecret,
		consumerKey:       consumerKey,
		projectId:         projectId,
		FakeApiResponses:  make(map[string]interface{}),
		FakeApiErrors:     make(map[string]error),
	}
}

func (p *MockProvider) GetName() (string, error) {
	if p.ShouldFail {
		return "", fmt.Errorf(p.FailureMessage)
	}
	return "ovh-mock", nil
}

func (p *MockProvider) ExecApi(method string, path string, body string) (interface{}, error) {
	key := fmt.Sprintf("%s:%s", method, path)
	
	// Check if we should return an error for this specific call
	if err, exists := p.FakeApiErrors[key]; exists {
		return nil, err
	}
	
	// Check if we have a fake response for this specific call
	if response, exists := p.FakeApiResponses[key]; exists {
		return response, nil
	}
	
	// Default fake responses for common edge cases
	switch path {
	case "/cloud/project":
		return p.getFakeProjectsResponse(), nil
	default:
		return nil, fmt.Errorf("mock: no fake response configured for %s %s", method, path)
	}
}

func (p *MockProvider) ExecApiProject(method string, path string, body string) (interface{}, error) {
	if p.projectId == "" {
		return nil, fmt.Errorf("OVH project id not configured")
	}
	fullPath := fmt.Sprintf("/cloud/project/%s%s", p.projectId, path)
	return p.ExecApi(method, fullPath, body)
}

// Helper methods to set up different edge case scenarios

// SetupEmptyProjectsList configures mock to return empty projects list
func (p *MockProvider) SetupEmptyProjectsList() {
	p.FakeApiResponses["GET:/cloud/project"] = []interface{}{}
}

// SetupInvalidProjectsFormat configures mock to return invalid format
func (p *MockProvider) SetupInvalidProjectsFormat() {
	p.FakeApiResponses["GET:/cloud/project"] = map[string]interface{}{
		"error": "invalid format",
		"code":  500,
	}
}

// SetupMixedTypeProjects configures mock to return mixed types (some strings, some numbers)
func (p *MockProvider) SetupMixedTypeProjects() {
	p.FakeApiResponses["GET:/cloud/project"] = []interface{}{
		"valid-project-1",
		123456, // This should cause a type assertion error
		"valid-project-2",
		map[string]string{"invalid": "object"},
	}
}

// SetupNetworkError configures mock to return network-like errors
func (p *MockProvider) SetupNetworkError() {
	p.FakeApiErrors["GET:/cloud/project"] = fmt.Errorf("network error: connection timeout")
}

// SetupAuthError configures mock to return authentication errors
func (p *MockProvider) SetupAuthError() {
	p.FakeApiErrors["GET:/cloud/project"] = fmt.Errorf("authentication failed: invalid consumer key")
}

// SetupLargeProjectsList configures mock to return a very large list
func (p *MockProvider) SetupLargeProjectsList() {
	projects := make([]interface{}, 1000)
	for i := 0; i < 1000; i++ {
		projects[i] = fmt.Sprintf("project-%d", i)
	}
	p.FakeApiResponses["GET:/cloud/project"] = projects
}

// SetupNullResponse configures mock to return null
func (p *MockProvider) SetupNullResponse() {
	p.FakeApiResponses["GET:/cloud/project"] = nil
}

func (p *MockProvider) getFakeProjectsResponse() interface{} {
	// Default fake response - normal case
	return []interface{}{
		"project-1",
		"project-2", 
		"project-3",
	}
}
