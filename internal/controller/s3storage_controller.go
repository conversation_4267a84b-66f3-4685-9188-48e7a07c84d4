package controller

import (
	"context"
	"fmt"

	"k8s.io/apimachinery/pkg/runtime"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	logf "sigs.k8s.io/controller-runtime/pkg/log"

	s3managerv1 "gitlab.mtk.zone/mt-public/s3manager/api/v1"
)

// S3StorageReconciler reconciles a S3Storage object
type S3StorageReconciler struct {
	client.Client
	Scheme *runtime.Scheme
}

// +kubebuilder:rbac:groups=s3manager.mtk.zone,resources=s3storages,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=s3manager.mtk.zone,resources=s3storages/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=s3manager.mtk.zone,resources=s3storages/finalizers,verbs=update

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
//
// For more details, check Reconcile and its Result here:
// - https://pkg.go.dev/sigs.k8s.io/controller-runtime@v0.20.4/pkg/reconcile
func (r *S3StorageReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	log := logf.Log.WithName(fmt.Sprintf("%s/%s", req.Namespace, req.Name))
	log.Info("Reconcile triggered")

	return ctrl.Result{}, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *S3StorageReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&s3managerv1.S3Storage{}).
		Named("s3storage").
		Complete(r)
}
