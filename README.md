# s3manager
// TODO(user): Add simple overview of use/purpose

## Getting Started

### Prerequisites
- go version v1.23.0+
- docker version 17.03+.
- kubectl version v1.11.3+.
- Access to a Kubernetes v1.11.3+ cluster.


**Install the CRDs into the cluster:**

```sh
./config/update-crd.sh
```

## Development

**Create and delete test resources:**

```sh
kubectl --context mv apply -f config/samples/s3storage.yaml
kubectl --context mv delete -f config/samples/s3storage.yaml
```

**Start the controller:**

```sh
KUBECONFIG=~/.kube/mv.yaml go run ./cmd/main.go
```

