name: Lint

on:
  push:
  pull_request:

jobs:
  lint:
    name: Run on Ubuntu
    runs-on: ubuntu-latest
    steps:
      - name: Clone the code
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version-file: go.mod

      - name: <PERSON> linter
        uses: golangci/golangci-lint-action@v6
        with:
          version: v1.63.4
