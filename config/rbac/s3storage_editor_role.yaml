# This rule is not used by the project s3manager itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants permissions to create, update, and delete resources within the s3manager.mtk.zone.
# This role is intended for users who need to manage these resources
# but should not control RBAC or manage permissions for others.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: s3manager
    app.kubernetes.io/managed-by: kustomize
  name: s3storage-editor-role
rules:
- apiGroups:
  - s3manager.mtk.zone
  resources:
  - s3storages
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - s3manager.mtk.zone
  resources:
  - s3storages/status
  verbs:
  - get
