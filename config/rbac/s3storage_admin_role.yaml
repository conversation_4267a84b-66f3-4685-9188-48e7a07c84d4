# This rule is not used by the project s3manager itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants full permissions ('*') over s3manager.mtk.zone.
# This role is intended for users authorized to modify roles and bindings within the cluster,
# enabling them to delegate specific permissions to other users or groups as needed.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: s3manager
    app.kubernetes.io/managed-by: kustomize
  name: s3storage-admin-role
rules:
- apiGroups:
  - s3manager.mtk.zone
  resources:
  - s3storages
  verbs:
  - '*'
- apiGroups:
  - s3manager.mtk.zone
  resources:
  - s3storages/status
  verbs:
  - get
