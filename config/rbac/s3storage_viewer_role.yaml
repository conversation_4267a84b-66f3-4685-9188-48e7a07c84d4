# This rule is not used by the project s3manager itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants read-only access to s3manager.mtk.zone resources.
# This role is intended for users who need visibility into these resources
# without permissions to modify them. It is ideal for monitoring purposes and limited-access viewing.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: s3manager
    app.kubernetes.io/managed-by: kustomize
  name: s3storage-viewer-role
rules:
- apiGroups:
  - s3manager.mtk.zone
  resources:
  - s3storages
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - s3manager.mtk.zone
  resources:
  - s3storages/status
  verbs:
  - get
