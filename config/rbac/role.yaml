---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - s3manager.mtk.zone
  resources:
  - s3storages
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - s3manager.mtk.zone
  resources:
  - s3storages/finalizers
  verbs:
  - update
- apiGroups:
  - s3manager.mtk.zone
  resources:
  - s3storages/status
  verbs:
  - get
  - patch
  - update
