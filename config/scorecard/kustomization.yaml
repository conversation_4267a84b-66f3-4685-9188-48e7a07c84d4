resources:
- bases/config.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
patches:
- path: patches/basic.config.yaml
  target:
    group: scorecard.operatorframework.io
    kind: Configuration
    name: config
    version: v1alpha3
- path: patches/olm.config.yaml
  target:
    group: scorecard.operatorframework.io
    kind: Configuration
    name: config
    version: v1alpha3
# +kubebuilder:scaffold:patches
